"""
对称加密工具类
用于账号密码的加密存储和解密
"""
import base64
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import Optional
import os


class CryptoUtils:
    """对称加密工具类"""
    
    def __init__(self, password: str):
        """
        初始化加密工具
        
        Args:
            password: 用于加密的密码（二级密码）
        """
        self.password = password.encode('utf-8')
        self._key = self._derive_key()
        self._fernet = Fernet(self._key)
    
    def _derive_key(self) -> bytes:
        """
        从密码派生加密密钥
        
        Returns:
            bytes: 派生的密钥
        """
        # 使用固定的盐值，确保相同密码生成相同密钥
        # 在生产环境中，建议使用随机盐值并存储
        salt = b'linuxdo_adt_salt_2024'
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return key
    
    def encrypt(self, plaintext: str) -> str:
        """
        加密字符串
        
        Args:
            plaintext: 要加密的明文字符串
            
        Returns:
            str: base64编码的加密字符串
        """
        if not plaintext:
            return ""
        
        try:
            # 加密
            encrypted_bytes = self._fernet.encrypt(plaintext.encode('utf-8'))
            # 转换为base64字符串
            encrypted_b64 = base64.b64encode(encrypted_bytes).decode('utf-8')
            return encrypted_b64
        except Exception as e:
            raise ValueError(f"加密失败: {str(e)}")
    
    def decrypt(self, encrypted_text: str) -> str:
        """
        解密字符串
        
        Args:
            encrypted_text: base64编码的加密字符串
            
        Returns:
            str: 解密后的明文字符串
        """
        if not encrypted_text:
            return ""
        
        try:
            # 从base64解码
            encrypted_bytes = base64.b64decode(encrypted_text.encode('utf-8'))
            # 解密
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            # 转换为字符串
            plaintext = decrypted_bytes.decode('utf-8')
            return plaintext
        except Exception as e:
            raise ValueError(f"解密失败: {str(e)}")
    
    @staticmethod
    def is_encrypted(text: str) -> bool:
        """
        判断字符串是否为加密格式
        
        Args:
            text: 要检查的字符串
            
        Returns:
            bool: 是否为加密格式
        """
        if not text:
            return False
        
        try:
            # 尝试base64解码
            decoded = base64.b64decode(text.encode('utf-8'))
            # 检查是否符合Fernet格式（以特定字节开头）
            return len(decoded) > 32 and decoded[0] == 0x80
        except Exception:
            return False


class PasswordManager:
    """密码管理器，处理加密存储逻辑"""
    
    def __init__(self, db_session, secondary_password: Optional[str] = None):
        """
        初始化密码管理器
        
        Args:
            db_session: 数据库会话
            secondary_password: 二级密码，如果为None则不加密
        """
        self.db = db_session
        self.secondary_password = secondary_password
        self.crypto = CryptoUtils(secondary_password) if secondary_password else None
    
    def encrypt_password(self, password: str) -> str:
        """
        加密密码
        
        Args:
            password: 明文密码
            
        Returns:
            str: 加密后的密码（如果未设置二级密码则返回原密码）
        """
        if not self.crypto:
            return password
        
        return self.crypto.encrypt(password)
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """
        解密密码
        
        Args:
            encrypted_password: 加密的密码
            
        Returns:
            str: 解密后的密码（如果未设置二级密码或密码未加密则返回原密码）
        """
        if not self.crypto:
            return encrypted_password
        
        # 检查是否为加密格式
        if not CryptoUtils.is_encrypted(encrypted_password):
            return encrypted_password
        
        return self.crypto.decrypt(encrypted_password)
    
    def get_secondary_password_from_settings(self) -> Optional[str]:
        """
        从系统设置中获取二级密码
        
        Returns:
            Optional[str]: 二级密码，如果未设置则返回None
        """
        from .models import SystemSettings
        
        setting = self.db.query(SystemSettings).filter(
            SystemSettings.key == "secondary_password"
        ).first()
        
        if setting and setting.value.strip():
            return setting.value.strip()
        
        return None
    
    @classmethod
    def create_from_db(cls, db_session):
        """
        从数据库设置创建密码管理器实例
        
        Args:
            db_session: 数据库会话
            
        Returns:
            PasswordManager: 密码管理器实例
        """
        instance = cls(db_session)
        secondary_password = instance.get_secondary_password_from_settings()
        
        if secondary_password:
            instance.secondary_password = secondary_password
            instance.crypto = CryptoUtils(secondary_password)
        
        return instance


def get_password_manager(db_session):
    """
    获取密码管理器实例的便捷函数
    
    Args:
        db_session: 数据库会话
        
    Returns:
        PasswordManager: 密码管理器实例
    """
    return PasswordManager.create_from_db(db_session)
